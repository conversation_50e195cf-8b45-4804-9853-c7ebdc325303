'use client'

'use client'

import { useState, useMemo } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Calendar, Clock, ArrowRight, Filter, User, BookOpen, Loader2 } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useBlogs } from '@/hooks/use-api'
import { formatDate } from '@/lib/utils'


export default function BlogGrid() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [page, setPage] = useState(1)

  // Fetch blogs with API
  const { data: blogsData, isLoading, error } = useBlogs({
    page,
    limit: 12,
    status: 'PUBLISHED',
    ...(selectedCategory !== 'all' && { category: selectedCategory })
  })

  const blogs = useMemo(() => blogsData?.blogs || [], [blogsData?.blogs])
  const pagination = blogsData?.pagination

  // Extract categories from blogs
  const categories = useMemo(() => {
    if (!blogs.length) return []

    const categoryMap = new Map()
    blogs.forEach(blog => {
      if (blog.category) {
        const categoryKey = blog.category.slug
        const categoryName = blog.category.name
        if (!categoryMap.has(categoryKey)) {
          categoryMap.set(categoryKey, { id: categoryKey, name: categoryName, count: 0 })
        }
        categoryMap.get(categoryKey).count++
      }
    })

    return Array.from(categoryMap.values())
  }, [blogs])

  const featuredPost = blogs.find(blog => blog.featured)
  const regularPosts = blogs.filter(blog => selectedCategory !== 'all' || !blog.featured)

  // Loading state
  if (isLoading) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Latest Articles"
            title="Beauty Blog & Tips"
            description="Stay updated with the latest beauty trends, professional tips, and expert advice to enhance your makeup skills and knowledge."
          />
        </AnimatedElement>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-3 text-text-secondary">Loading articles...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Latest Articles"
            title="Beauty Blog & Tips"
            description="Stay updated with the latest beauty trends, professional tips, and expert advice to enhance your makeup skills and knowledge."
          />
        </AnimatedElement>
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <BookOpen className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            Unable to Load Articles
          </h3>
          <p className="text-text-secondary mb-6">
            We&apos;re having trouble loading the blog articles. Please try again later.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Section>
    )
  }

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Latest Articles"
          title="Beauty Blog & Tips"
          description="Stay updated with the latest beauty trends, professional tips, and expert advice to enhance your makeup skills and knowledge."
        />
      </AnimatedElement>

      {/* Category Filter */}
      <AnimatedElement animation="slideUp" delay={0.3} className="mb-12">
        <div className="flex flex-wrap justify-center gap-3">
          <Button
            variant={selectedCategory === 'all' ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory('all')}
            className="group"
          >
            <Filter className="w-4 h-4 mr-2" />
            All Articles
            <Badge variant="secondary" className="ml-2 text-xs">
              {blogs.length}
            </Badge>
          </Button>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category.id)}
              className="group"
            >
              <Filter className="w-4 h-4 mr-2" />
              {category.name}
              <Badge variant="secondary" className="ml-2 text-xs">
                {category.count}
              </Badge>
            </Button>
          ))}
        </div>
      </AnimatedElement>

      {/* Featured Post */}
      {selectedCategory === 'all' && featuredPost && (
        <AnimatedElement animation="slideUp" delay={0.4} className="mb-12">
            <Card className="overflow-hidden border-0 bg-gradient-to-br from-rose-gold/10 to-blush-pink/10">
              <div className="grid lg:grid-cols-2 gap-0">
            <Card className="overflow-hidden border-0 bg-gradient-to-br from-rose-gold/10 to-blush-pink/10">
              <div className="grid lg:grid-cols-2 gap-0">
                <div className="relative aspect-[16/9] lg:aspect-auto">
                  <Image
                    src={featuredPost.image || "https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=400&fit=crop&crop=face&q=80"}
                    alt={featuredPost.title}
                    fill
                    className="object-cover"
                  />
                  <Badge className="absolute top-4 left-4" variant="default">
                    Featured
                  </Badge>
                </div>
                <div className="p-8 flex flex-col justify-center">
                  {featuredPost.category && (
                    <Badge variant="secondary" className="w-fit mb-3">
                      {featuredPost.category.name}
                    </Badge>
                  )}
                  <h2 className="font-display text-2xl md:text-3xl font-bold text-text-primary mb-4">
                    {featuredPost.title}
                  </h2>
                  <p className="text-text-secondary mb-6 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-text-muted mb-6">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4" />
                      <span>{featuredPost.author}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      <span>{formatDate(featuredPost.publishedAt || featuredPost.createdAt)}</span>
                    </div>
                    {featuredPost.readTime && (
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4" />
                        <span>{featuredPost.readTime}</span>
                      </div>
                    )}
                  </div>
                  <Button asChild variant="gradient" className="w-fit group">
                    <Link href={`/blog/${featuredPost.slug}`}>
                      Read Full Article
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </div>
            </Card>
              </div>
            </Card>
        </AnimatedElement>
      )}

      {/* Blog Posts Grid */}
      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {regularPosts.map((post) => (
          <StaggeredItem key={post.id}>
            <Card className="group h-full hover:shadow-xl transition-all duration-300 border-0 bg-white overflow-hidden">
              {/* Post Image */}
              <div className="relative aspect-[16/9] overflow-hidden">
                <Image
                  src={post.image || "https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=250&fit=crop&crop=face&q=80"}
                  alt={post.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />

                {/* Category Badge */}
                {post.category && (
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="text-xs bg-white/90 text-text-primary">
                      {post.category.name}
                    </Badge>
                  </div>
                )}

                {/* Featured Badge */}
                {post.featured && (
                  <div className="absolute top-4 right-4">
                    <Badge variant="default" className="text-xs">
                      Featured
                    </Badge>
                  </div>
                )}
              </div>
              
              <CardHeader className="pb-3">
                <CardTitle className="text-xl group-hover:text-rose-gold-dark transition-colors line-clamp-2">
                  {post.title}
                </CardTitle>
                <CardDescription className="text-text-secondary line-clamp-3">
                  {post.excerpt}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4 flex-1 flex flex-col">
                {/* Post Meta */}
                <div className="flex items-center gap-4 text-sm text-text-muted">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{post.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    <span>{post.readTime}</span>
                  </div>
                </div>

                <div className="flex items-center gap-2 text-sm text-text-muted">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {post.tags.slice(0, 3).map((tagRelation) => (
                    <Badge key={tagRelation.tag.id} variant="outline" className="text-xs">
                      {tagRelation.tag.name}
                    </Badge>
                  ))}
                </div>
                
                {/* Read More Button */}
                <div className="pt-4 mt-auto">
                  <Button 
                    asChild 
                    variant="outline" 
                    className="w-full group"
                    size="sm"
                  >
                    <Link href={`/blog/${post.slug}`}>
                      Read Article
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* No Posts Message */}
      {blogs.length === 0 && (
        <AnimatedElement animation="slideUp" delay={0.6} className="text-center py-12">
          <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-4">
            <BookOpen className="w-8 h-8 text-white" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            No Articles Found
          </h3>
          <p className="text-text-secondary mb-6">
            No articles found in this category. Try selecting a different category or check back later for new content.
          </p>
          <Button 
            variant="outline" 
            onClick={() => setSelectedCategory('all')}
          >
            View All Articles
          </Button>
        </AnimatedElement>
      )}

      {/* Pagination */}
      {pagination && pagination.pages > 1 && (
        <AnimatedElement animation="slideUp" delay={0.6} className="mt-12 text-center">
          <p className="text-text-secondary mb-6">
            Showing {blogs.length} of {pagination.total} articles
          </p>
          <div className="flex justify-center gap-2">
            <Button
              variant="outline"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Previous
            </Button>
            <span className="flex items-center px-4 text-sm text-text-secondary">
              Page {page} of {pagination.pages}
            </span>
            <Button
              variant="outline"
              onClick={() => setPage(page + 1)}
              disabled={page === pagination.pages}
            >
              Next
            </Button>
          </div>
        </AnimatedElement>
      )}
    </Section>
  )
}
