'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { <PERSON>, Filter, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { ImageModal } from '@/components/ui/image-modal'
import { useGallery, useGalleryByCategory } from '@/hooks/use-api'
import { trackPortfolioImageView, trackGalleryFilter } from '@/lib/analytics'

// Fallback categories if API fails
const fallbackCategories = [
  { id: 'all', name: 'All', count: 0 },
  { id: 'bridal', name: 'Bridal', count: 0 },
  { id: 'party', name: 'Party', count: 0 },
  { id: 'engagement', name: 'Engagement', count: 0 },
  { id: 'traditional', name: 'Traditional', count: 0 },
]

export default function PortfolioGallery() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [categories, setCategories] = useState(fallbackCategories)

  // Fetch all gallery items for initial load and category counts
  const {
    data: galleryData,
    isLoading: isGalleryLoading,
    error: galleryError
  } = useGallery({ status: 'ACTIVE' })

  // Fetch gallery items by category when selectedCategory changes
  const {
    data: categoryData,
    isLoading: isCategoryLoading,
    error: categoryError
  } = useGalleryByCategory(selectedCategory !== 'all' ? selectedCategory : '')

  // Determine which data to use based on selected category
  const isLoading = selectedCategory === 'all' ? isGalleryLoading : isCategoryLoading
  const error = selectedCategory === 'all' ? galleryError : categoryError

  // Use the appropriate data source based on the selected category
  const galleryItems = selectedCategory === 'all'
    ? (galleryData?.gallery || [])
    : (categoryData?.gallery || [])

  // Update category counts based on API data
  useEffect(() => {
    if (galleryData?.gallery) {
      // Count items per category
      const categoryCounts: Record<string, number> = { 'all': galleryData.gallery.length }

      galleryData.gallery.forEach(item => {
        if (item.category) {
          categoryCounts[item.category] = (categoryCounts[item.category] || 0) + 1
        }
      })

      // Create categories array with counts
      const updatedCategories = [
        { id: 'all', name: 'All', count: categoryCounts['all'] || 0 },
        ...Object.keys(categoryCounts)
          .filter(key => key !== 'all')
          .map(categoryId => ({
            id: categoryId,
            name: categoryId.charAt(0).toUpperCase() + categoryId.slice(1),
            count: categoryCounts[categoryId]
          }))
      ]

      setCategories(updatedCategories)
    }
  }, [galleryData])

  // Modal handlers
  const openModal = (index: number) => {
    const item = galleryItems[index]
    if (item) {
      trackPortfolioImageView(item.id, item.category || 'unknown')
    }
    setCurrentImageIndex(index)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
  }

  // Loading state
  if (isLoading) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Our Work"
            title="Portfolio Gallery"
            description="Browse through our collection of stunning makeup transformations. Each image tells a story of beauty, confidence, and artistry."
          />
        </AnimatedElement>

        <div className="flex justify-center items-center py-12">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-2 text-text-secondary">Loading gallery...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Our Work"
            title="Portfolio Gallery"
            description="Browse through our collection of stunning makeup transformations. Each image tells a story of beauty, confidence, and artistry."
          />
        </AnimatedElement>

        <div className="text-center py-12">
          <p className="text-red-600 mb-4">Failed to load gallery. Please try again later.</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Retry
          </Button>
        </div>
      </Section>
    )
  }

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Work"
          title="Portfolio Gallery"
          description="Browse through our collection of stunning makeup transformations. Each image tells a story of beauty, confidence, and artistry."
        />
      </AnimatedElement>

      {/* Category Filter */}
      <AnimatedElement animation="slideUp" delay={0.3} className="mb-12">
        <div className="flex flex-wrap justify-center gap-3">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setSelectedCategory(category.id)
                trackGalleryFilter(category.name)
              }}
              className="group"
            >
              <Filter className="w-4 h-4 mr-2" />
              {category.name}
              <Badge variant="secondary" className="ml-2 text-xs">
                {category.count}
              </Badge>
            </Button>
          ))}
        </div>
      </AnimatedElement>

      {/* Gallery Grid */}
      <StaggeredContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {galleryItems.map((item, index) => (
          <StaggeredItem key={item.id}>
            <div
              className="group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 cursor-pointer"
              onClick={() => openModal(index)}
            >
              <Image
                src={item.image || `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=400&fit=crop&crop=face&q=80`}
                alt={item.title}
                fill
                className="object-cover group-hover:scale-110 transition-transform duration-500"
              />
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <Badge variant="secondary" className="mb-3 text-xs">
                    {item.category}
                  </Badge>
                  <h3 className="font-display text-lg font-semibold mb-2">
                    {item.title}
                  </h3>
                  <p className="text-sm text-white/80 mb-3">
                    {item.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs border-white/30 text-white">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* View Icon */}
              <div className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Eye className="w-5 h-5 text-white" />
              </div>

              {/* Featured Badge */}
              {item.featured && (
                <div className="absolute top-4 left-4">
                  <Badge variant="default" className="text-xs">
                    Featured
                  </Badge>
                </div>
              )}
            </div>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Masonry Layout for larger screens */}
      <div className="hidden 2xl:block mt-16">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            title="Featured Showcase"
            description="A curated selection of our most stunning transformations in a beautiful masonry layout."
          />
        </AnimatedElement>

        <StaggeredContainer className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
          {galleryItems.filter(item => item.featured).map((item, index) => {
            const heights = ['aspect-[3/4]', 'aspect-square', 'aspect-[4/5]', 'aspect-[3/5]']
            const randomHeight = heights[index % heights.length]
            
            return (
              <StaggeredItem key={`masonry-${item.id}`}>
                <div
                  className={`group relative ${randomHeight} overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 cursor-pointer break-inside-avoid`}
                  onClick={() => openModal(galleryItems.findIndex(galleryItem => galleryItem.id === item.id))}
                >
                  <Image
                    src={item.image || `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=500&fit=crop&crop=face&q=80`}
                    alt={item.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                      <Badge variant="secondary" className="mb-2 text-xs">
                        {item.category}
                      </Badge>
                      <h3 className="font-display text-base font-semibold mb-1">
                        {item.title}
                      </h3>
                      <p className="text-xs text-white/80">
                        {item.description}
                      </p>
                    </div>
                  </div>

                  {/* View Icon */}
                  <div className="absolute top-3 right-3 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Eye className="w-4 h-4 text-white" />
                  </div>
                </div>
              </StaggeredItem>
            )
          })}
        </StaggeredContainer>
      </div>

      {/* Load More Button */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-12 text-center">
        <p className="text-text-secondary mb-6">
          Showing {galleryItems.length} images
        </p>
        <Button variant="outline" size="lg" disabled>
          <Eye className="w-5 h-5 mr-2" />
          Load More Coming Soon
        </Button>
      </AnimatedElement>

      {/* Image Modal */}
      <ImageModal
        isOpen={isModalOpen}
        onClose={closeModal}
        images={galleryItems}
        currentIndex={currentImageIndex}
        onIndexChange={setCurrentImageIndex}
      />
    </Section>
  )
}
