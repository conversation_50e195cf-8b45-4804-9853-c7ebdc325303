'use client'

import Link from 'next/link'
import Image from 'next/image'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { getPopularServices } from '@/lib/data'
import { formatPrice } from '@/lib/utils'

export default function ServicesOverview() {
  const popularServices = getPopularServices()
  console.log(popularServices)

  return (
    <Section background="cream" id="services">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Services"
          title="Professional Makeup Services"
          description="From bridal elegance to party glamour, we offer comprehensive makeup services tailored to your unique style and occasion."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {popularServices.map((service) => (
          <StaggeredItem key={service.id}>
            <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
              <div className="relative aspect-[4/3] overflow-hidden rounded-t-xl">
                <Image
                  src={`https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop&crop=face&q=80`}
                  alt={service.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                {service.popular && (
                  <Badge className="absolute top-3 left-3" variant="default">
                    <Star className="w-3 h-3 mr-1 fill-current" />
                    Popular
                  </Badge>
                )}
              </div>
              
              <CardHeader className="pb-3">
                <CardTitle className="text-xl group-hover:text-rose-gold-dark transition-colors">
                  {service.title}
                </CardTitle>
                <CardDescription className="text-text-secondary">
                  {service.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2 text-text-secondary">
                    <Clock className="w-4 h-4" />
                    <span>{service.duration}</span>
                  </div>
                  <div className="font-semibold text-rose-gold-dark">
                    {formatPrice(service.price)}
                  </div>
                </div>
                
                <ul className="space-y-1">
                  {service.features.slice(0, 3).map((feature, idx) => (
                    <li key={idx} className="text-sm text-text-secondary flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-rose-gold rounded-full flex-shrink-0"></div>
                      {feature}
                    </li>
                  ))}
                  {service.features.length > 3 && (
                    <li className="text-sm text-text-muted">
                      +{service.features.length - 3} more features
                    </li>
                  )}
                </ul>
                
                <Button asChild variant="outline" className="w-full group">
                  <Link href={`/services#${service.id}`}>
                    Learn More
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      <AnimatedElement animation="slideUp" delay={0.6} className="text-center">
        <Button asChild variant="gradient" size="lg">
          <Link href="/services">
            View All Services
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
        </Button>
      </AnimatedElement>
    </Section>
  )
}
