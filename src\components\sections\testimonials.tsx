'use client'
/* eslint-disable @typescript-eslint/no-explicit-any */

import Image from 'next/image'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { useApprovedTestimonials } from '@/hooks/use-api'
import { useState } from 'react'

// Fallback testimonials when API is not available
const fallbackTestimonials = [
  {
    id: 'fallback-1',
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    message: '<PERSON><PERSON><PERSON> did an amazing job on my wedding day! The makeup was flawless and lasted the entire day. I felt like a princess. Highly recommended for all brides!',
    rating: 5,
    service: 'Bridal Makeup',
    specialties: ['Bridal Look', 'Traditional Style', 'Long-lasting'],
    image: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-2',
    name: '<PERSON><PERSON>',
    message: 'Perfect makeup for my engagement party. Anjali understood exactly what I wanted and delivered beyond my expectations. The team is professional and talented.',
    rating: 5,
    service: 'Party Makeup',
    specialties: ['Party Look', 'Modern Style', 'Professional'],
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 'fallback-3',
    name: 'Kamala Thapa',
    email: '<EMAIL>',
    message: 'I had my makeup done for a traditional ceremony and it was absolutely beautiful. Anjali respects cultural traditions while adding her modern touch.',
    rating: 5,
    service: 'Traditional Makeup',
    specialties: ['Cultural Traditions', 'Modern Touch', 'Ceremony Ready'],
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face&q=80',
    status: 'APPROVED' as const,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
]

export default function Testimonials() {
  const { data: testimonialsData } = useApprovedTestimonials(6)
  const [currentIndex, setCurrentIndex] = useState(0)

  // Use API data if available, otherwise use fallback testimonials
  const testimonials = (testimonialsData?.testimonials && testimonialsData.testimonials.length > 0)
    ? testimonialsData.testimonials
    : fallbackTestimonials

  const isUsingFallback = !testimonialsData?.testimonials || testimonialsData.testimonials.length === 0

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
  }

  const currentTestimonial = testimonials[currentIndex]

  return (
    <Section background="gradient" id="testimonials">
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Client Reviews"
          title="What Our Clients Say"
          description={isUsingFallback
            ? "Read what our satisfied clients have to say about their experience with our professional makeup services. (Sample reviews - connect to CMS for live testimonials)"
            : "Read what our satisfied clients have to say about their experience with our professional makeup services."
          }
        />
      </AnimatedElement>

      {/* Desktop Layout - Side by Side */}
      <AnimatedElement animation="slideUp" delay={0.2} className="mt-16">
        <div className="max-w-6xl mx-auto">
          {/* Mobile Layout - Stacked */}
          <div className="block lg:hidden">
            <div className="relative max-w-2xl mx-auto">
              {/* Profile Image with Navigation Arrows */}
              <div className="relative mb-8">
                <div className="relative w-full aspect-[3/4] max-w-sm mx-auto rounded-2xl overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink">
                  <Image
                    src={currentTestimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face&q=80`}
                    alt={currentTestimonial.name}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Navigation Arrows positioned relative to image */}
                <button
                  onClick={prevTestimonial}
                  className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                <button
                  onClick={nextTestimonial}
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-white/80 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>

              {/* Testimonial Content */}
              <div className="space-y-6 text-center">
                {/* Large Quote Mark */}
                <div className="text-4xl text-rose-gold/30 font-serif leading-none">
                  &ldquo;
                </div>

                {/* Client Name and Service */}
                <div className="space-y-2">
                  <h3 className="text-xl font-bold text-text-primary">
                    {currentTestimonial.name}
                  </h3>
                  {currentTestimonial.service && (
                    <p className="text-blue-600 font-medium">
                      {currentTestimonial.service}
                    </p>
                  )}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-base text-text-secondary leading-relaxed">
                  {currentTestimonial.message}
                </blockquote>

                {/* Specialties */}
                {(currentTestimonial as any).specialties && (
                  <div className="space-y-3">
                    <h4 className="text-sm font-semibold text-text-primary">Specialties:</h4>
                    <div className="flex flex-wrap gap-2 justify-center">
                      {(currentTestimonial as any).specialties.map((specialty: string, index: number) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-blue-600 text-white text-sm rounded-full font-medium"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Navigation Dots */}
                <div className="flex items-center justify-center gap-2 pt-4">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === currentIndex
                          ? 'bg-blue-600'
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Layout - Side by Side with External Navigation */}
          <div className="hidden lg:block relative">
            {/* Navigation Arrows - Outside the content */}
            <button
              onClick={prevTestimonial}
              className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-16 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white z-10"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>

            <button
              onClick={nextTestimonial}
              className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-16 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center text-text-primary hover:bg-white z-10"
            >
              <ChevronRight className="w-6 h-6" />
            </button>

            {/* 50-50 Split Container */}
            <div className="flex items-center">
              {/* Left Side - Image (50%) */}
              <div className="w-1/2 pr-8">
                <div className="relative w-full h-[500px] rounded-2xl overflow-hidden bg-gradient-to-br from-rose-gold to-blush-pink shadow-2xl">
                  <Image
                    src={currentTestimonial.image || `https://images.unsplash.com/photo-1494790108755-2616b612b786?w=600&h=750&fit=crop&crop=face&q=80`}
                    alt={currentTestimonial.name}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>

              {/* Right Side - Content (50%) */}
              <div className="w-1/2 pl-8">
                {/* Large Quote Mark */}
                <div className="text-[120px] text-blue-200/60 font-serif leading-none mb-6 -mt-4">
                  &ldquo;
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-xl text-text-secondary leading-relaxed mb-8 -mt-8">
                  {currentTestimonial.message}
                </blockquote>

                {/* Client Name and Service */}
                <div className="space-y-2 mb-6">
                  <h3 className="text-2xl font-bold text-text-primary">
                    {currentTestimonial.name}
                  </h3>
                  {currentTestimonial.service && (
                    <p className="text-blue-600 font-semibold text-lg">
                      {currentTestimonial.service}
                    </p>
                  )}
                </div>

                {/* Specialties - Only show if it's team data (fallback) */}
                {(currentTestimonial as any).specialties && isUsingFallback && (
                  <div className="space-y-3 mb-6">
                    <h4 className="text-sm font-semibold text-text-primary">Specialties:</h4>
                    <div className="flex flex-wrap gap-2">
                      {(currentTestimonial as any).specialties.map((specialty: string, index: number) => (
                        <span
                          key={index}
                          className="px-4 py-2 bg-blue-600 text-white text-sm rounded-full font-medium"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Navigation Dots */}
                <div className="flex items-center gap-3">
                  {testimonials.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentIndex(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === currentIndex
                          ? 'bg-blue-600'
                          : 'bg-gray-300 hover:bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </AnimatedElement>

      {/* Stats Section */}
      <AnimatedElement animation="slideUp" delay={0.8} className="mt-24">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">50+</div>
            <div className="text-text-secondary">Happy Clients</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">5.0</div>
            <div className="text-text-secondary">Average Rating</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">100+</div>
            <div className="text-text-secondary">Makeup Sessions</div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl md:text-4xl font-bold text-text-primary">7</div>
            <div className="text-text-secondary">Cities Served</div>
          </div>
        </div>
      </AnimatedElement>
    </Section>
  )
}
