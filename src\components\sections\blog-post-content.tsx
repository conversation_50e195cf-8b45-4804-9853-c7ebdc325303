'use client'

import Link from 'next/link'
import Image from 'next/image'
import { ArrowLeft, Calendar, Clock, User, Share2, Tag } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section } from '@/components/ui/section'
import { AnimatedElement } from '@/components/ui/animated-element'
import { BlogPost, getRelatedBlogPosts } from '@/lib/data'
import { formatDate } from '@/lib/utils'

interface BlogPostContentProps {
  post: BlogPost
}

export default function BlogPostContent({ post }: BlogPostContentProps) {
  const relatedPosts = getRelatedBlogPosts(post)

  // Mock blog content since we're using JSON instead of actual markdown files
  const mockContent = `
    <p>This is a sample blog post content. In a real implementation, this would be loaded from a markdown file or CMS.</p>
    
    <h2>Introduction</h2>
    <p>Welcome to this comprehensive guide about ${post.title.toLowerCase()}. In this article, we'll explore various aspects and provide you with expert insights.</p>
    
    <h2>Key Points</h2>
    <ul>
      <li>Professional techniques and tips</li>
      <li>Step-by-step guidance</li>
      <li>Common mistakes to avoid</li>
      <li>Product recommendations</li>
    </ul>
    
    <h2>Expert Tips</h2>
    <p>Based on years of experience in the beauty industry, here are some professional insights that can help you achieve better results.</p>
    
    <blockquote>
      <p>"The key to great makeup is understanding your unique features and enhancing them naturally." - Professional Makeup Artist</p>
    </blockquote>
    
    <h2>Conclusion</h2>
    <p>We hope this guide has been helpful in your beauty journey. Remember, practice makes perfect, and don't hesitate to reach out for professional advice.</p>
  `

  return (
    <>
      {/* Hero Section */}
      <Section className="pt-24 pb-8">
        <div className="max-w-4xl mx-auto">
          <AnimatedElement animation="slideUp">
            <Button asChild variant="ghost" className="mb-6">
              <Link href="/blog">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Blog
              </Link>
            </Button>
            
            <Badge variant="secondary" className="mb-4">
              {post.category}
            </Badge>
            
            <h1 className="font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary leading-tight mb-6">
              {post.title}
            </h1>
            
            <p className="text-xl text-text-secondary leading-relaxed mb-8">
              {post.excerpt}
            </p>
            
            {/* Post Meta */}
            <div className="flex flex-wrap items-center gap-6 text-sm text-text-muted mb-8">
              <div className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span>{post.author}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(post.publishedAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                <span>{post.readTime}</span>
              </div>
              <Button variant="ghost" size="sm">
                <Share2 className="w-4 h-4 mr-2" />
                Share
              </Button>
            </div>
          </AnimatedElement>

          {/* Featured Image */}
          <AnimatedElement animation="slideUp" delay={0.3}>
            <div className="relative aspect-[16/9] rounded-xl overflow-hidden mb-8">
              <Image
                src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&h=450&fit=crop&crop=face&q=80"
                alt={post.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          </AnimatedElement>
        </div>
      </Section>

      {/* Article Content */}
      <Section className="py-0">
        <div className="max-w-4xl mx-auto">
          <div className="grid lg:grid-cols-4 gap-12">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <AnimatedElement animation="slideUp" delay={0.4}>
                <div 
                  className="prose prose-lg max-w-none prose-headings:font-display prose-headings:text-text-primary prose-p:text-text-secondary prose-a:text-rose-gold-dark prose-blockquote:border-rose-gold prose-blockquote:bg-cream prose-blockquote:p-4 prose-blockquote:rounded-lg"
                  dangerouslySetInnerHTML={{ __html: mockContent }}
                />
              </AnimatedElement>

              {/* Tags */}
              <AnimatedElement animation="slideUp" delay={0.6} className="mt-12 pt-8 border-t border-gray-200">
                <div className="flex items-center gap-3 mb-4">
                  <Tag className="w-5 h-5 text-text-secondary" />
                  <span className="font-semibold text-text-primary">Tags:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </AnimatedElement>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-8">
                {/* Author Info */}
                <AnimatedElement animation="slideLeft" delay={0.5}>
                  <Card>
                    <CardContent className="p-6 text-center">
                      <div className="w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-4">
                        <User className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="font-semibold text-text-primary mb-2">{post.author}</h3>
                      <p className="text-text-secondary text-sm mb-4">
                        Professional makeup artist with 5+ years of experience in beauty industry.
                      </p>
                      <Button asChild variant="outline" size="sm" className="w-full">
                        <Link href="/about">
                          Learn More
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </AnimatedElement>

                {/* Related Posts */}
                {relatedPosts.length > 0 && (
                  <AnimatedElement animation="slideLeft" delay={0.7}>
                    <Card>
                      <CardContent className="p-6">
                        <h3 className="font-semibold text-text-primary mb-4">Related Articles</h3>
                        <div className="space-y-4">
                          {relatedPosts.map((relatedPost) => (
                            <Link 
                              key={relatedPost.id}
                              href={`/blog/${relatedPost.slug}`}
                              className="block group"
                            >
                              <div className="flex gap-3">
                                <div className="relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                                  <Image
                                    src="https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=100&h=100&fit=crop&crop=face&q=80"
                                    alt={relatedPost.title}
                                    fill
                                    className="object-cover"
                                  />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <h4 className="font-medium text-text-primary text-sm group-hover:text-rose-gold-dark transition-colors line-clamp-2">
                                    {relatedPost.title}
                                  </h4>
                                  <p className="text-text-muted text-xs mt-1">
                                    {formatDate(relatedPost.publishedAt)}
                                  </p>
                                </div>
                              </div>
                            </Link>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </AnimatedElement>
                )}
              </div>
            </div>
          </div>
        </div>
      </Section>

      {/* CTA Section */}
      <Section background="cream" className="mt-16">
        <div className="max-w-4xl mx-auto text-center">
          <AnimatedElement animation="slideUp">
            <h2 className="font-display text-2xl md:text-3xl font-bold text-text-primary mb-4">
              Ready to Transform Your Look?
            </h2>
            <p className="text-text-secondary mb-8">
              Book a professional makeup session and experience the difference expert artistry can make.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild variant="gradient" size="lg">
                <Link href="/contact">
                  Book Consultation
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg">
                <Link href="/services">
                  View Services
                </Link>
              </Button>
            </div>
          </AnimatedElement>
        </div>
      </Section>
    </>
  )
}
